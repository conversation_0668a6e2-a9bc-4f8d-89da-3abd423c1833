import i18n from "i18next";
import { initReactI18next } from "react-i18next";
import { getLocales } from "expo-localization";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { LANGUAGE_KEY, resources, SupportedLanguages } from ".";

const getInitialLanguage = async () => {
  const storedLanguage = await AsyncStorage.getItem(LANGUAGE_KEY);
  if (storedLanguage) {
    return storedLanguage;
  }
  const deviceLanguage = getLocales()?.[0]?.languageCode || "pt";
  return resources[deviceLanguage as SupportedLanguages]
    ? deviceLanguage
    : "pt";
};

const initI18n = async () => {
  const initialLanguage = await getInitialLanguage();

  await i18n.use(initReactI18next).init({
    resources,
    lng: initialLanguage,
    fallbackLng: "pt",
    compatibilityJSON: "v4",
    interpolation: {
      escapeValue: false,
    },
    debug: __DEV__,
  });
};

initI18n();

export default i18n;
