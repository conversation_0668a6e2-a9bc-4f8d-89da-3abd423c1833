import useGetQuery from "@/hooks/useGetQuery";
import { z } from "zod";

export const eventCheckoutMethodsSchema = z.object({
  name: z.string(),
  type: z.enum(["WHATSAPP", "WEBSITE", "EMAIL", "PHONE"]),
  value: z.string(),
});

export type EventCheckoutMethodsType = z.infer<
  typeof eventCheckoutMethodsSchema
>;

export const myEventSchema = z.object({
  _id: z.string(),
  _type: z.literal("event"),
  _createdAt: z.string().datetime(),
  _updatedAt: z.string().datetime(),
  name: z.string().max(50, "Nome deve ter no máximo 50 caracteres"),
  slug: z.string(),
  organizer: z
    .string()
    .max(50, "Nome do organizador deve ter no máximo 50 caracteres"),
  description: z
    .string()
    .max(500, "Descrição deve ter no máximo 500 caracteres"),
  location: z.string().max(100, "Local deve ter no máximo 100 caracteres"),
  startAt: z.string().datetime(),
  endAt: z.string().datetime(),
  highlightedUntil: z.string().datetime().optional().nullable(),
  sponsoredUntil: z.string().datetime().optional().nullable(),
  checkoutMethods: z.array(eventCheckoutMethodsSchema).optional().nullable(),
  prices: z.array(
    z.object({
      name: z.string().max(50),
      price: z.number(),
      description: z
        .string()
        .max(250, "Descrição deve ter no máximo 250 caracteres")
        .optional()
        .nullable(),
    })
  ),
  categories: z
    .array(
      z.object({
        _id: z.string(),
        name: z.string(),
      })
    )
    .optional()
    .nullable(),
  medias: z
    .array(
      z.object({
        url: z.string(),
        urlSmall: z.string().optional(),
        urlBanner: z.string().optional(),
      })
    )
    .optional()
    .nullable(),
  createdBy: z
    .string()
    .max(50, "Nome do criador deve ter no máximo 50 caracteres"),
});

export type MyEventType = z.infer<typeof myEventSchema>;

// filters

export type EventFilterFields = {
  limit?: number;
  searchKey?: string;
  categoryId?: string;
  organizer?: string;
  startAt?: string;
  sortBy?: keyof Omit<MyEventType, "createdBy" | "medias" | "categories">;
  sortOrder?: "asc" | "desc";
  highlightedUntil?: boolean;
  sponsoredUntil?: boolean;
  location?: string;
};

// hooks
type Props = EventFilterFields;

const useGetEvents = (query?: Props) => {
  const { data, ...rest } = useGetQuery<MyEventType[]>({
    queryKey: "events",
    apiUrl: "events",
    initialData: [],
    query,
    enabled: true,
  });

  return {
    events: data,
    ...rest,
  };
};

export default useGetEvents;
