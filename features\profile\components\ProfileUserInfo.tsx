import Gap from "@/components/Gap";
import { MyUserProfileType } from "@/hooks/useAuth";
import globalStyles from "@/lib/globalStyles";
import React from "react";
import { Linking, Text, View } from "react-native";

const ProfileUserInfo = ({ user }: { user: MyUserProfileType }) => {
  const handleLink = (url: string) =>
    url.startsWith("http://") || url.startsWith("https://")
      ? Linking.openURL(url)
      : Linking.openURL(`https://${url}`);

  return (
    <View style={{ flexDirection: "column", justifyContent: "center" }}>
      <Text
        style={{
          fontSize: globalStyles.size["6xl"],
          fontWeight: "bold",
          color: globalStyles.colors.secondary1,
        }}
      >
        {user.name}
      </Text>
      <Gap y={5} />
      <Text
        style={{
          fontSize: globalStyles.size.md,
          color: globalStyles.colors.light.secondary,
        }}
      >
        {user.email}
      </Text>
      <Gap y={5} />

      {/* {user.phone && (
        <Text
          style={{
            fontSize: globalStyles.size.md,
            color: globalStyles.colors.light.secondary,
          }}
        >
          {user.phone}
        </Text>
      )} */}
      <Gap y={5} />
      {/* {user.website && (
        <Text
          onPress={() => handleLink(user.website!)}
          style={{
            fontSize: globalStyles.size.md,
            color: globalStyles.colors.primary2,
          }}
        >
          {user.website}
        </Text>
      )} */}
    </View>
  );
};

export default ProfileUserInfo;
