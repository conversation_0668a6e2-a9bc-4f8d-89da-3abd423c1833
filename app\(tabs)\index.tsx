import Button from "@/components/Button";
import EventsList from "@/components/event/EventsList";
import EventSponsoredList from "@/components/event/EventSponsoredList";
import HighlightEvents from "@/components/event/HighlightEvents";
import Gap from "@/components/Gap";
import Layout from "@/components/Layout";
import SectionHeading from "@/components/SectionHeader";
import AdsBanner from "@/features/ads/AdsBanner";
import useGetEvents from "@/features/events/model";
import useAuth from "@/hooks/useAuth";
import globalStyles from "@/lib/globalStyles";
import { router } from "expo-router";
import { useLayoutEffect } from "react";
import { Text } from "react-native";
import { useTranslation } from "react-i18next";

export default function HomePage() {
  const { user, isLoadingUser } = useAuth();
  const { t } = useTranslation();
  const { events, handleRefresh, isLoading, isFetching } = useGetEvents({
    limit: 10,
    sortBy: "startAt",
  });

  const {
    events: highlightedEvents,
    isLoading: isEvtHighlightedLoading,
    isFetching: isEvtHighlightedFetching,
  } = useGetEvents({ limit: 10, highlightedUntil: true });

  const {
    events: sponsoredEvents,
    isLoading: isEvtSponsoredLoading,
    isFetching: isEvtSponsoredFetching,
  } = useGetEvents({ limit: 1, sponsoredUntil: true });

  useLayoutEffect(() => {
    handleRefresh();
  }, []);

  return (
    <Layout
      backButton={false}
      title={{
        text: isLoadingUser
          ? t("common.loading")
          : user?.name
          ? t("home.title", { name: user.firstName })
          : t("auth.sign_in"),
        onPress:
          user || isLoadingUser ? undefined : () => router.push("/sign-in"),
      }}
      isRefreshing={isLoading || isEvtHighlightedLoading}
      onRefresh={handleRefresh}
      showSearch
      noMargin={<AdsBanner />}
    >
      <SectionHeading children={<Text>{t("common.highlight")}</Text>} />
      <Gap y={globalStyles.gap["xs"]} />
      <EventSponsoredList
        events={sponsoredEvents}
        isLoading={isEvtSponsoredFetching || isEvtSponsoredLoading}
      />
      <Gap y={globalStyles.gap["xs"]} />
      <HighlightEvents
        events={highlightedEvents}
        isLoading={isEvtHighlightedLoading || isEvtHighlightedFetching}
      />
      <Gap y={globalStyles.gap["2xs"]} />
      <SectionHeading children={<Text>{t("home.upcoming")}</Text>} />
      <Gap y={globalStyles.gap["xs"]} />
      <EventsList
        events={events}
        isFetching={isFetching}
        isLoading={isLoading}
      />
      <Gap y={globalStyles.gap.xs} />
      <Button
        text={t("home.see_all_events")}
        onPress={() =>
          router.push({ pathname: "/search", params: { showAll: "true" } })
        }
      />
      <Gap y={globalStyles.gap.xs} />
    </Layout>
  );
}
