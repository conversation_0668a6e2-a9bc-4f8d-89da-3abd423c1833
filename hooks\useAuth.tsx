import { useState, useEffect } from "react";
import { SignedIn, SignedOut, useClerk, useUser } from "@clerk/clerk-expo";
import { router } from "expo-router";
import { createHandleErrorDialog } from "@/lib/errors";
import { z } from "zod";
import { API_URLS, fetchApi } from "@/config/api";

export const myUserProfileSchema = z.object({
  id: z.string({ message: "Id inválido" }),
  name: z.string({ message: "Nome inválido" }),
  firstName: z
    .string()
    .min(2, "Nome deve ter no mínimo 2 caracteres")
    .max(50, "Nome deve ter no máximo 50 caracteres"),
  lastName: z
    .string()
    .min(2, "Sobrenome deve ter no mínimo 2 caracteres")
    .max(50, "Sobrenome deve ter no máximo 50 caracteres"),
  email: z.string().email("Email inválido"),
  emailVerified: z.boolean(),
  phoneNumber: z.string().length(9, "Número de telefone inválido").optional(),
  website: z.string().url("Website inválido").optional(),
  gender: z.enum(["male", "female", "prefer_not_say"]).optional(),
  birthday: z.coerce.date().optional(),
});

export type MyUserProfileType = z.infer<typeof myUserProfileSchema>;

const toMyUserProfile = (user: any): MyUserProfileType | null => {
  if (!user) return null;
  return {
    id: user.id,
    email: user.emailAddresses[0].emailAddress,
    emailVerified: user.emailAddresses[0].verification.status === "verified",
    name: user.fullName,
    firstName: user.firstName,
    lastName: user.lastName,
    phoneNumber: user.unsafeMetadata.phoneNumber,
    website: user.unsafeMetadata.website,
    gender: user.unsafeMetadata.gender,
    birthday: user.unsafeMetadata.birthday
      ? new Date(user.unsafeMetadata.birthday)
      : undefined,
  };
};

const useAuth = () => {
  const { user, isLoaded, isSignedIn } = useUser();
  const { signOut } = useClerk();

  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [_user, setUser] = useState<MyUserProfileType | null>(null);
  const [isLoadingUser, setIsLoadingUser] = useState(isLoaded);

  useEffect(() => {
    if (isLoaded) {
      setIsAuthenticated(isSignedIn);
      setUser(toMyUserProfile(user));
      setIsLoadingUser(false);
    }
  }, [isLoaded, isSignedIn, user]);

  const handleSignOut = () => {
    signOut()
      .catch(async (err) => {
        createHandleErrorDialog({
          title: "Erro ao terminar sessão",
          error: err,
        });
      })
      .then(() => router.replace("/sign-in"));
  };

  const deleteUser = async () => {
    try {
      setIsLoadingUser(true);
      if (!_user?.id) {
        throw new Error("User not found");
      }

      await fetchApi(API_URLS.users, {
        method: "DELETE",
        body: JSON.stringify({ userId: _user.id }),
      });

      await handleSignOut();
    } catch (err) {
      createHandleErrorDialog({
        title: "Erro ao excluir conta",
        message:
          "Ocorreu um erro ao excluir a sua conta. Tente novamente mais tarde.",
        error: err as Error,
      });
      throw err;
    } finally {
      setIsLoadingUser(false);
    }
  };

  return {
    user: _user,
    isSignedIn: isAuthenticated,
    isLoadingUser,
    signOut: handleSignOut,
    deleteUser,
    SignedOut,
    SignedIn,
    userClient: user,
  };
};

export default useAuth;
