// import InputSelect from "@/components/InputSelect";
import MyDatePicker from "@/components/MyDatePicker";
import useEventFilterStore from "@/features/events/useEventFilterStore";
import useGetEventsCategories from "@/features/events/useGetEventsCategories";
import useGetEventsLocations from "@/features/events/useGetEventsLocations";
import useGetEventsOrganizers from "@/features/events/useGetEventsOrganizers";
import React, { useMemo } from "react";
import { Modal, StyleSheet, Text, TouchableOpacity, View } from "react-native";

type Props = {
  isOpen: boolean;
  onClose: () => void;
};

const FilterModalAction = ({
  onClean,
  onConclude,
}: {
  onClean: () => void;
  onConclude: () => void;
}) => {
  return (
    <View style={{ flexDirection: "row", justifyContent: "space-between" }}>
      <TouchableOpacity onPress={() => onClean()}>
        <Text className="text-primary1">Limpar</Text>
      </TouchableOpacity>
      <TouchableOpacity onPress={onConclude}>
        <Text className="text-primary1">Concluir</Text>
      </TouchableOpacity>
    </View>
  );
};

const EventFilterModal = ({ isOpen, onClose }: Props) => {
  const {
    clearFilter,
    sortBy,
    selectedStartDate,
    selectedOrganizer,
    selectedLocation,
    selectedCategory,
    setSelectedOrganizer,
    setSelectedCategory,
    setStartDate,
    setFilter,
  } = useEventFilterStore();

  const { categories } = useGetEventsCategories();

  const { organizers } = useGetEventsOrganizers();

  const { locations } = useGetEventsLocations();

  const organizersItems = useMemo(
    () => organizers.map((v) => ({ name: v.name, value: v.name })),
    [organizers]
  );

  const categoriesItems = useMemo(
    () =>
      categories.map((item) => ({
        name: item.name,
        value: item._id,
      })),

    [categories]
  );

  const locationsItems = useMemo(
    () => locations.map((item) => ({ name: item.name, value: item.name })),
    [locations]
  );

  // const handleConclude = () => {
  //   const isSearchScreen = navigation.getState()?.routeNames[0] === "Pesquisa";

  //   onClose();
  //   if (sortBy && !isSearchScreen) {
  //     navigateTo("Pesquisar");
  //   }
  // };

  return (
    <Modal
      animationType="slide"
      transparent={true}
      visible={isOpen}
      onRequestClose={onClose}
    >
      <View className="flex flex-1 w-full h-full justify-center items-center mt-5 shadow-lg">
        <View
          style={{ elevation: 5 }}
          className="flex m-5 min-w-[50%] max-w-[80%] p-xs bg-white rounded-sm shadow-lg"
        >
          <Text className="text-tertiary2 text-xl font-bold">
            Filtrar eventos
          </Text>
          {/* <InputSelect
            label="Categorias"
            items={categoriesItems}
            selected={selectedCategory?._id}
            onSelect={(value) => {
              setSelectedCategory(categories.find((c) => c._id === value));
            }}
          />
          <InputSelect
            label="Por Organizador"
            items={organizersItems}
            selectedName={selectedOrganizer}
            onSelect={(v) => setSelectedOrganizer(v)}
          />
          <InputSelect
            label="Por Localização"
            items={locationsItems}
            selected={selectedLocation}
            onSelect={(v) => setFilter("selectedLocation", v)}
          /> */}
          <MyDatePicker
            label="Por Data"
            date={selectedStartDate || new Date()}
            onChange={(timestamp) => setStartDate(new Date(timestamp))}
          ></MyDatePicker>
          <FilterModalAction
            onClean={clearFilter}
            // onConclude={handleConclude}
            onConclude={onClose}
          />
        </View>
      </View>
      <View className="absolute inset-0 bg-black/15 -z-10" />
    </Modal>
  );
};

// const styles = StyleSheet.create({
//   centeredView: {
//     flex: 1,
//     flexDirection: "row",
//     justifyContent: "center",
//     alignItems: "center",
//     marginTop: 22,
//     elevation: 5,
//   },
//   modalView: {
//     justifyContent: "flex-start",
//     margin: 20,
//     minWidth: "50%",
//     width: "60%",
//     maxWidth: "80%",
//     backgroundColor: "white",
//     borderRadius: globalStyles.rounded.sm,
//     padding: globalStyles.gap.xs,
//     shadowColor: "#000",
//     shadowOffset: {
//       width: 0,
//       height: 2,
//     },
//     shadowOpacity: 0.25,
//     shadowRadius: 4,
//     elevation: 5,
//   },
//   backdrop: {
//     zIndex: -1,
//     position: "absolute",
//     top: 0,
//     left: 0,
//     right: 0,
//     bottom: 0,
//     backgroundColor: "rgba(0,0,0,0.15)",
//   },
// });

export default EventFilterModal;
